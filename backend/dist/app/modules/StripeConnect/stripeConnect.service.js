"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StripeConnectService = void 0;
const stripe_1 = __importDefault(require("stripe"));
const http_status_1 = __importDefault(require("http-status"));
const AppError_1 = __importDefault(require("../../errors/AppError"));
const teacher_model_1 = require("../Teacher/teacher.model");
const stripe = new stripe_1.default(process.env.STRIPE_SECRET_KEY, {
    apiVersion: '2024-06-20',
});
// Create Stripe Connect account
const createStripeAccount = (teacherId, accountData) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        console.log('Creating Stripe account for teacher:', teacherId);
        // Check if teacher already has a Stripe account
        const teacher = yield teacher_model_1.Teacher.findById(teacherId);
        if (!teacher) {
            throw new AppError_1.default(http_status_1.default.NOT_FOUND, 'Teacher not found');
        }
        if (teacher.stripeAccountId) {
            throw new AppError_1.default(http_status_1.default.BAD_REQUEST, 'Teacher already has a Stripe account');
        }
        // Create Stripe Express account
        const account = yield stripe.accounts.create({
            type: accountData.type,
            country: accountData.country,
            email: accountData.email,
            business_type: accountData.business_type || 'individual',
            capabilities: {
                card_payments: { requested: true },
                transfers: { requested: true },
            },
            metadata: {
                teacherId: teacherId,
                platform: 'LMS',
            },
        });
        // Update teacher with Stripe account ID
        yield teacher_model_1.Teacher.findByIdAndUpdate(teacherId, {
            stripeAccountId: account.id,
        });
        console.log('Stripe account created successfully:', account.id);
        return {
            accountId: account.id,
            isConnected: true,
            isVerified: account.details_submitted && account.charges_enabled,
            canReceivePayments: account.charges_enabled && account.payouts_enabled,
            requirements: account.requirements,
        };
    }
    catch (error) {
        console.error('Error creating Stripe account:', error);
        if (error instanceof AppError_1.default) {
            throw error;
        }
        throw new AppError_1.default(http_status_1.default.INTERNAL_SERVER_ERROR, `Failed to create Stripe account: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
});
// Create account link for onboarding
const createAccountLink = (teacherId, linkData) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const teacher = yield teacher_model_1.Teacher.findById(teacherId);
        if (!teacher || !teacher.stripeAccountId) {
            throw new AppError_1.default(http_status_1.default.BAD_REQUEST, 'No Stripe account found for this teacher');
        }
        const accountLink = yield stripe.accountLinks.create({
            account: teacher.stripeAccountId,
            refresh_url: linkData.refreshUrl,
            return_url: linkData.returnUrl,
            type: linkData.type,
        });
        return {
            url: accountLink.url,
            expiresAt: accountLink.expires_at,
        };
    }
    catch (error) {
        console.error('Error creating account link:', error);
        if (error instanceof AppError_1.default) {
            throw error;
        }
        throw new AppError_1.default(http_status_1.default.INTERNAL_SERVER_ERROR, `Failed to create account link: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
});
// Get account status
const getAccountStatus = (teacherId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const teacher = yield teacher_model_1.Teacher.findById(teacherId);
        if (!teacher) {
            throw new AppError_1.default(http_status_1.default.NOT_FOUND, 'Teacher not found');
        }
        if (!teacher.stripeAccountId) {
            return {
                isConnected: false,
                isVerified: false,
                canReceivePayments: false,
                accountId: null,
                requirements: null,
            };
        }
        const account = yield stripe.accounts.retrieve(teacher.stripeAccountId);
        return {
            isConnected: true,
            isVerified: account.details_submitted && account.charges_enabled,
            canReceivePayments: account.charges_enabled && account.payouts_enabled,
            accountId: account.id,
            requirements: account.requirements,
            capabilities: account.capabilities,
            country: account.country,
            defaultCurrency: account.default_currency,
            email: account.email,
            businessProfile: account.business_profile,
        };
    }
    catch (error) {
        console.error('Error getting account status:', error);
        if (error instanceof AppError_1.default) {
            throw error;
        }
        throw new AppError_1.default(http_status_1.default.INTERNAL_SERVER_ERROR, `Failed to get account status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
});
// Update account information
const updateAccount = (teacherId, updateData) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const teacher = yield teacher_model_1.Teacher.findById(teacherId);
        if (!teacher || !teacher.stripeAccountId) {
            throw new AppError_1.default(http_status_1.default.BAD_REQUEST, 'No Stripe account found for this teacher');
        }
        const updatedAccount = yield stripe.accounts.update(teacher.stripeAccountId, updateData);
        return {
            accountId: updatedAccount.id,
            businessProfile: updatedAccount.business_profile,
            capabilities: updatedAccount.capabilities,
            requirements: updatedAccount.requirements,
        };
    }
    catch (error) {
        console.error('Error updating account:', error);
        if (error instanceof AppError_1.default) {
            throw error;
        }
        throw new AppError_1.default(http_status_1.default.INTERNAL_SERVER_ERROR, `Failed to update account: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
});
// Delete/disconnect account
const disconnectAccount = (teacherId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const teacher = yield teacher_model_1.Teacher.findById(teacherId);
        if (!teacher || !teacher.stripeAccountId) {
            throw new AppError_1.default(http_status_1.default.BAD_REQUEST, 'No Stripe account found for this teacher');
        }
        // Delete the Stripe account
        yield stripe.accounts.del(teacher.stripeAccountId);
        // Remove Stripe account ID from teacher
        yield teacher_model_1.Teacher.findByIdAndUpdate(teacherId, {
            $unset: { stripeAccountId: 1 },
        });
        return { success: true, message: 'Stripe account disconnected successfully' };
    }
    catch (error) {
        console.error('Error disconnecting account:', error);
        if (error instanceof AppError_1.default) {
            throw error;
        }
        throw new AppError_1.default(http_status_1.default.INTERNAL_SERVER_ERROR, `Failed to disconnect account: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
});
exports.StripeConnectService = {
    createStripeAccount,
    createAccountLink,
    getAccountStatus,
    updateAccount,
    disconnectAccount,
};
